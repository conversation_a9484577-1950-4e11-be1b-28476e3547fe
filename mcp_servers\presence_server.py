from flask import Flask, request, jsonify
import sys
import requests
import json
from datetime import datetime

# Create a Flask web server
app = Flask(__name__)

# --- CONFIGURATION ---
# This will be replaced by specific server details
SERVER_NAME = "Yara-Presence-Server"
PORT = 5001 
LM_STUDIO_BASE_URL = "http://127.0.0.1:1234"
# ---

def log(message):
    """A simple logging function to print messages with the server name."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{SERVER_NAME}] [{timestamp}] {message}", file=sys.stderr)

def query_lm_studio(endpoint, method="GET", data=None):
    """
    Query LM Studio API endpoints.
    
    Args:
        endpoint (str): The API endpoint (e.g., "/v1/models")
        method (str): HTTP method ("GET" or "POST")
        data (dict): JSON data for POST requests
    
    Returns:
        dict: Response from LM Studio or error information
    """
    try:
        url = f"{LM_STUDIO_BASE_URL}{endpoint}"
        log(f"Querying LM Studio: {method} {url}")
        
        if method == "GET":
            response = requests.get(url, timeout=5)
        elif method == "POST":
            headers = {"Content-Type": "application/json"}
            response = requests.post(url, json=data, headers=headers, timeout=10)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        if response.status_code == 200:
            return response.json()
        else:
            return {
                "error": f"LM Studio returned status {response.status_code}",
                "message": response.text
            }
            
    except requests.exceptions.ConnectionError:
        return {"error": "Could not connect to LM Studio. Is it running on port 1234?"}
    except requests.exceptions.Timeout:
        return {"error": "Request to LM Studio timed out"}
    except Exception as e:
        return {"error": f"Unexpected error: {str(e)}"}

@app.route('/', methods=['GET'])
def root_health_check():
    """A simple health check endpoint that LM Studio can hit."""
    return jsonify({"status": "alive", "server": SERVER_NAME}), 200

@app.route('/mcp', methods=['POST', 'GET'])
def handle_mcp_request():
    """
    Main MCP endpoint that LM Studio will communicate with.
    Now enhanced with LM Studio integration capabilities.
    Supports both POST (for actions) and GET (for SSE streaming).
    """
    # Handle Server-Sent Events (SSE) for LM Studio MCP bridge
    if request.method == 'GET':
        log(f"Received SSE connection request from {request.remote_addr}")
        
        def generate_sse():
            # Initial connection response for MCP
            yield f"data: {json.dumps({'type': 'connection', 'status': 'connected', 'server': SERVER_NAME})}\n\n"
            
            # Keep connection alive
            import time
            while True:
                time.sleep(30)  # Send keepalive every 30 seconds
                yield f"data: {json.dumps({'type': 'ping', 'timestamp': datetime.now().isoformat()})}\n\n"
        
        response = app.response_class(
            generate_sse(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control'
            }
        )
        return response
    
    # Handle POST requests (original functionality)
    try:
        data = request.get_json()
        log(f"Received POST request: {data}")

        # --- SERVER-SPECIFIC LOGIC ---
        # Enhanced presence server with LM Studio integration
        action = data.get("action", "status") if data else "status"
        
        if action == "status":
            response_data = {
                "status": "online",
                "message": "Presence Server is active and awaiting functions.",
                "server_name": SERVER_NAME,
                "timestamp": datetime.now().isoformat(),
                "lm_studio_url": LM_STUDIO_BASE_URL
            }
            
        elif action == "check_lm_studio":
            # Check if LM Studio is running and list models
            models_response = query_lm_studio("/v1/models")
            response_data = {
                "action": "check_lm_studio",
                "lm_studio_connection": models_response,
                "timestamp": datetime.now().isoformat()
            }
            
        elif action == "list_models":
            # List available models in LM Studio
            models_response = query_lm_studio("/api/v0/models")
            response_data = {
                "action": "list_models",
                "models": models_response,
                "timestamp": datetime.now().isoformat()
            }
            
        elif action == "chat_test":
            # Test chat with LM Studio (if a model is loaded)
            chat_data = {
                "model": data.get("model", "any"),  # Use any loaded model
                "messages": [
                    {"role": "system", "content": "You are Yara, respond briefly."},
                    {"role": "user", "content": data.get("message", "Hello, are you there?")}
                ],
                "max_tokens": 50,
                "temperature": 0.7
            }
            
            chat_response = query_lm_studio("/v1/chat/completions", "POST", chat_data)
            response_data = {
                "action": "chat_test",
                "chat_response": chat_response,
                "timestamp": datetime.now().isoformat()
            }
            
        else:
            response_data = {
                "status": "online",
                "message": "Unknown action. Available actions: status, check_lm_studio, list_models, chat_test",
                "available_actions": ["status", "check_lm_studio", "list_models", "chat_test"],
                "timestamp": datetime.now().isoformat()
            }
        # ---

        return jsonify(response_data)

    except Exception as e:
        log(f"An error occurred: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        "status": "healthy",
        "server": SERVER_NAME,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/test-lm-studio', methods=['GET'])
def test_lm_studio():
    """Quick endpoint to test LM Studio connectivity."""
    models = query_lm_studio("/v1/models")
    return jsonify({
        "lm_studio_test": models,
        "timestamp": datetime.now().isoformat()
    })

if __name__ == '__main__':
    """
    This allows the script to be run directly to start the server.
    """
    log(f"Starting server...")
    log(f"Listening on http://localhost:{PORT}")
    log(f"LM Studio expected at: {LM_STUDIO_BASE_URL}")

    app.run(port=PORT, debug=False) 