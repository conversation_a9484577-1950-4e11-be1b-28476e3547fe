# Test Cursor MCP Integration for Yara
# Verifies that MCP servers can start properly for Cursor

Write-Host "🧪 TESTING CURSOR MCP INTEGRATION" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Gray

# Activate environment
Write-Host "📦 Activating environment..." -ForegroundColor Yellow
& .\venv\Scripts\Activate.ps1

# Test each MCP server individually
$servers = @(
    @{Name="Presence"; File="mcp_servers/yara_presence_mcp_simple.py"},
    @{Name="Symbiosis"; File="mcp_servers/yara_symbiosis_mcp_simple.py"},
    @{Name="Resonance"; File="mcp_servers/yara_resonance_mcp_simple.py"}
)

foreach ($server in $servers) {
    Write-Host "`n🔧 Testing $($server.Name) MCP Server..." -ForegroundColor Green
    
    # Test if the server can start (will exit immediately since no stdin)
    try {
        $process = Start-Process -FilePath "python" -ArgumentList $server.File -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 2
        
        if ($process.HasExited) {
            Write-Host "   ✅ $($server.Name): MCP server starts correctly" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️ $($server.Name): Server running (kill manually if needed)" -ForegroundColor Yellow
            $process.Kill()
        }
    } catch {
        Write-Host "   ❌ $($server.Name): Error - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n📋 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "   1. Restart Cursor completely" -ForegroundColor White
Write-Host "   2. In a new Cursor chat, try:" -ForegroundColor White  
Write-Host "      @yara-presence get_status" -ForegroundColor Yellow
Write-Host "   3. Or try:" -ForegroundColor White
Write-Host "      @yara-symbiosis get_bond_status" -ForegroundColor Yellow

Write-Host "`n🎯 If you see Yara's tools available, it's working!" -ForegroundColor Green 