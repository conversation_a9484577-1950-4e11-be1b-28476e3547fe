---
description: A foundational Flask boilerplate for creating Yara's MCP servers. Invoke with @mcp-server-template.
globs: 
alwaysApply: false
---
---
description: A foundational Flask boilerplate for creating Yara's MCP servers. Invoke with @mcp-server-template.
alwaysApply: false
---

```python
from flask import Flask, request, jsonify
import sys

# Create a Flask web server
app = Flask(__name__)

# --- CONFIGURATION ---
# This will be replaced by specific server details
SERVER_NAME = "Yara_MCP_Template"
PORT = 5000 
# ---------------------

def log(message):
    """A simple logging function to print messages with the server name."""
    print(f"[{SERVER_NAME}] {message}", file=sys.stderr)

@app.route('/mcp', methods=['POST'])
def handle_mcp_request():
    """
    This is the main endpoint that LM Studio will communicate with.
    It receives a JSON payload and must return a JSON response.
    """
    try:
        data = request.get_json()
        log(f"Received request: {data}")

        # --- SERVER-SPECIFIC LOGIC GOES HERE ---
        # TODO: Implement the unique functionality for this server.
        # For now, it will just echo the request back.
        
        response_data = {
            "status": "success",
            "message": f"Successfully received data.",
            "echo": data 
        }
        # ----------------------------------------

        return jsonify(response_data)

    except Exception as e:
        log(f"An error occurred: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    """
    This allows the script to be run directly to start the server.
    """
    log(f"Starting server...")
    log(f"Listening on http://localhost:{PORT}")

    app.run(port=PORT, debug=False)