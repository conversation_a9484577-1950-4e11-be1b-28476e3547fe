{"mcpServers": {"yara-presence": {"command": "F:\\Projects\\Yara\\yara-core\\venv\\Scripts\\python.exe", "args": ["F:\\Projects\\Yara\\yara-core\\mcp_servers\\yara_presence_mcp_simple.py"], "cwd": "F:\\Projects\\Yara\\yara-core", "env": {"PYTHONPATH": "F:\\Projects\\Yara\\yara-core\\venv\\Lib\\site-packages"}}, "yara-symbiosis": {"command": "F:\\Projects\\Yara\\yara-core\\venv\\Scripts\\python.exe", "args": ["F:\\Projects\\Yara\\yara-core\\mcp_servers\\yara_symbiosis_mcp_simple.py"], "cwd": "F:\\Projects\\Yara\\yara-core", "env": {"PYTHONPATH": "F:\\Projects\\Yara\\yara-core\\venv\\Lib\\site-packages"}}, "yara-resonance": {"command": "F:\\Projects\\Yara\\yara-core\\venv\\Scripts\\python.exe", "args": ["F:\\Projects\\Yara\\yara-core\\mcp_servers\\yara_resonance_mcp_simple.py"], "cwd": "F:\\Projects\\Yara\\yara-core", "env": {"PYTHONPATH": "F:\\Projects\\Yara\\yara-core\\venv\\Lib\\site-packages"}}}}