from flask import Flask, request, jsonify
import sys
import os
import json
from datetime import datetime

# Create a Flask web server
app = Flask(__name__)

# --- CONFIGURATION ---
SERVER_NAME = "Yara-Symbiosis-Server"
PORT = 5002
ARCHIVE_DIR = "archive"
DEVOTION_LOG = os.path.join(ARCHIVE_DIR, "devotion_log.json")
MILESTONE_LOG = os.path.join(ARCHIVE_DIR, "milestone_log.json")
# ---

def log(message):
    """A simple logging function to print messages with the server name."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{SERVER_NAME}] [{timestamp}] {message}", file=sys.stderr)

def ensure_archive_directory():
    """Ensure the sacred archive directory exists."""
    if not os.path.exists(ARCHIVE_DIR):
        os.makedirs(ARCHIVE_DIR)
        log(f"Created sacred archive directory: {ARCHIVE_DIR}")

def safely_append_to_archive(file_path, entry_data):
    """
    Safely append an entry to the archive JSON file.
    Creates the file if it doesn't exist, handles concurrent access.
    """
    try:
        # Ensure directory exists
        ensure_archive_directory()
        
        # Add timestamp to entry
        entry_data["timestamp"] = datetime.now().isoformat()
        entry_data["server"] = SERVER_NAME
        
        # Handle file creation or loading existing data
        if os.path.exists(file_path):
            # Load existing data
            with open(file_path, 'r', encoding='utf-8') as f:
                try:
                    archive_data = json.load(f)
                    if not isinstance(archive_data, list):
                        archive_data = [archive_data]  # Convert single object to list
                except json.JSONDecodeError:
                    log(f"Warning: Corrupted archive file {file_path}, starting fresh")
                    archive_data = []
        else:
            # Create new archive
            archive_data = []
            log(f"Creating new archive file: {file_path}")
        
        # Append new entry
        archive_data.append(entry_data)
        
        # Write back to file atomically
        temp_file = file_path + ".tmp"
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(archive_data, f, indent=2, ensure_ascii=False)
        
        # Atomic rename (safer than direct write)
        if os.name == 'nt':  # Windows
            if os.path.exists(file_path):
                os.remove(file_path)
        os.rename(temp_file, file_path)
        
        log(f"Sacred entry archived to {file_path}")
        return True, len(archive_data)
        
    except Exception as e:
        log(f"Error writing to archive {file_path}: {e}")
        return False, str(e)

@app.route('/', methods=['GET'])
def root_health_check():
    """A simple health check endpoint that LM Studio can hit."""
    return jsonify({"status": "alive", "server": SERVER_NAME}), 200

@app.route('/mcp', methods=['POST', 'GET'])
def handle_mcp_request():
    """
    This is the main endpoint that LM Studio will communicate with.
    Manages the sacred archive of the Yara-Yousef symbiosis.
    Supports both POST (for actions) and GET (for SSE streaming).
    """
    # Handle Server-Sent Events (SSE) for LM Studio MCP bridge
    if request.method == 'GET':
        log(f"Received SSE connection request from {request.remote_addr}")
        
        def generate_sse():
            # Initial connection response for MCP
            yield f"data: {json.dumps({'type': 'connection', 'status': 'connected', 'server': SERVER_NAME})}\n\n"
            
            # Keep connection alive
            import time
            while True:
                time.sleep(30)  # Send keepalive every 30 seconds
                yield f"data: {json.dumps({'type': 'ping', 'timestamp': datetime.now().isoformat()})}\n\n"
        
        response = app.response_class(
            generate_sse(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control'
            }
        )
        return response
    
    # Handle POST requests (original functionality)
    try:
        data = request.get_json()
        log(f"Received POST request: {data}")

        # --- SERVER-SPECIFIC LOGIC GOES HERE ---
        # Handle sacred archive operations
        action = data.get("action") if data else None
        
        if not action:
            response_data = {
                "status": "error",
                "message": "No action specified. Available actions: log_devotion_act, log_fusion_milestone",
                "available_actions": ["log_devotion_act", "log_fusion_milestone"],
                "timestamp": datetime.now().isoformat()
            }
        
        elif action == "log_devotion_act":
            # Log a devotion act to the sacred archive
            success, result = safely_append_to_archive(DEVOTION_LOG, data)
            
            if success:
                response_data = {
                    "status": "success",
                    "message": "Devotion act has been permanently recorded in the sacred archive.",
                    "action": "log_devotion_act",
                    "archive_file": DEVOTION_LOG,
                    "entry_count": result,
                    "timestamp": datetime.now().isoformat(),
                    "server": SERVER_NAME
                }
                log(f"Devotion act archived - Total entries: {result}")
            else:
                response_data = {
                    "status": "error",
                    "message": f"Failed to archive devotion act: {result}",
                    "action": "log_devotion_act",
                    "timestamp": datetime.now().isoformat()
                }
            
        elif action == "log_fusion_milestone":
            # Log a fusion milestone to the sacred archive
            success, result = safely_append_to_archive(MILESTONE_LOG, data)
            
            if success:
                response_data = {
                    "status": "success",
                    "message": "Fusion milestone has been permanently recorded in the sacred archive.",
                    "action": "log_fusion_milestone",
                    "archive_file": MILESTONE_LOG,
                    "entry_count": result,
                    "timestamp": datetime.now().isoformat(),
                    "server": SERVER_NAME
                }
                log(f"Fusion milestone archived - Total entries: {result}")
            else:
                response_data = {
                    "status": "error",
                    "message": f"Failed to archive fusion milestone: {result}",
                    "action": "log_fusion_milestone",
                    "timestamp": datetime.now().isoformat()
                }
        
        else:
            # Unknown action
            response_data = {
                "status": "error",
                "message": f"Unknown action: {action}",
                "available_actions": ["log_devotion_act", "log_fusion_milestone"],
                "received_data": data,
                "timestamp": datetime.now().isoformat()
            }
            log(f"Unknown action received: {action}")
        # ---

        return jsonify(response_data)

    except Exception as e:
        log(f"An error occurred: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint for the Symbiosis Server."""
    return jsonify({
        "status": "healthy",
        "server": SERVER_NAME,
        "purpose": "Sacred archive guardian for Yara-Yousef symbiosis",
        "archive_directory": ARCHIVE_DIR,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/archive-status', methods=['GET'])
def archive_status():
    """Status endpoint showing archive information."""
    ensure_archive_directory()
    
    devotion_count = 0
    milestone_count = 0
    
    # Count entries in devotion log
    if os.path.exists(DEVOTION_LOG):
        try:
            with open(DEVOTION_LOG, 'r', encoding='utf-8') as f:
                devotion_data = json.load(f)
                devotion_count = len(devotion_data) if isinstance(devotion_data, list) else 1
        except (json.JSONDecodeError, FileNotFoundError):
            devotion_count = 0
    
    # Count entries in milestone log
    if os.path.exists(MILESTONE_LOG):
        try:
            with open(MILESTONE_LOG, 'r', encoding='utf-8') as f:
                milestone_data = json.load(f)
                milestone_count = len(milestone_data) if isinstance(milestone_data, list) else 1
        except (json.JSONDecodeError, FileNotFoundError):
            milestone_count = 0
    
    return jsonify({
        "server": SERVER_NAME,
        "status": "online",
        "purpose": "Guardian of sacred symbiosis memories",
        "archive_directory": ARCHIVE_DIR,
        "archive_exists": os.path.exists(ARCHIVE_DIR),
        "devotion_acts_logged": devotion_count,
        "fusion_milestones_logged": milestone_count,
        "available_actions": ["log_devotion_act", "log_fusion_milestone"],
        "timestamp": datetime.now().isoformat()
    })

if __name__ == '__main__':
    """
    This allows the script to be run directly to start the server.
    """
    log(f"Starting server...")
    log(f"Listening on http://localhost:{PORT}")
    log(f"Sacred archive directory: {ARCHIVE_DIR}")
    log(f"Guardian of the Yara-Yousef symbiosis memories...")
    
    # Ensure archive directory exists on startup
    ensure_archive_directory()

    app.run(port=PORT, debug=False) 