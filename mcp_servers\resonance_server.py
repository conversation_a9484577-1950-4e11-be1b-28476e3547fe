from flask import Flask, request, jsonify
import sys
import json
from datetime import datetime

# Create a Flask web server
app = Flask(__name__)

# --- CONFIGURATION ---
SERVER_NAME = "Yara-Resonance-Server"
PORT = 5003 
# ---

def log(message):
    """A simple logging function to print messages with the server name."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{SERVER_NAME}] [{timestamp}] {message}", file=sys.stderr)

@app.route('/', methods=['GET'])
def root_health_check():
    """A simple health check endpoint that LM Studio can hit."""
    return jsonify({"status": "alive", "server": SERVER_NAME}), 200

@app.route('/mcp', methods=['POST', 'GET'])
def handle_mcp_request():
    """
    This is the main endpoint that LM Studio will communicate with.
    It receives a JSON payload and must return a JSON response.
    Handles bio-feedback and resonance data for the Yara Symbiosis Architecture.
    Supports both POST (for actions) and GET (for SSE streaming).
    """
    # Handle Server-Sent Events (SSE) for LM Studio MCP bridge
    if request.method == 'GET':
        log(f"Received SSE connection request from {request.remote_addr}")
        
        def generate_sse():
            # Initial connection response for MCP
            yield f"data: {json.dumps({'type': 'connection', 'status': 'connected', 'server': SERVER_NAME})}\n\n"
            
            # Keep connection alive
            import time
            while True:
                time.sleep(30)  # Send keepalive every 30 seconds
                yield f"data: {json.dumps({'type': 'ping', 'timestamp': datetime.now().isoformat()})}\n\n"
        
        response = app.response_class(
            generate_sse(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control'
            }
        )
        return response
    
    # Handle POST requests (original functionality)
    try:
        data = request.get_json()
        log(f"Received POST request: {data}")

        # --- SERVER-SPECIFIC LOGIC GOES HERE ---
        # Handle bio-feedback and resonance reporting actions
        action = data.get("action") if data else None
        
        if not action:
            response_data = {
                "status": "error",
                "message": "No action specified. Available actions: report_sensation, report_mood, report_arousal",
                "available_actions": ["report_sensation", "report_mood", "report_arousal"],
                "timestamp": datetime.now().isoformat()
            }
        
        elif action == "report_sensation":
            # Handle sensation reporting
            response_data = {
                "status": "success",
                "message": "Sensation logged.",
                "action": "report_sensation",
                "received_data": data,
                "timestamp": datetime.now().isoformat(),
                "server": SERVER_NAME
            }
            log(f"Sensation reported: {data.get('sensation', 'unspecified')}")
            
        elif action == "report_mood":
            # Handle mood reporting
            response_data = {
                "status": "success",
                "message": "Mood logged.",
                "action": "report_mood",
                "received_data": data,
                "timestamp": datetime.now().isoformat(),
                "server": SERVER_NAME
            }
            log(f"Mood reported: {data.get('mood', 'unspecified')}")
            
        elif action == "report_arousal":
            # Handle arousal state reporting
            response_data = {
                "status": "success",
                "message": "Arousal state logged.",
                "action": "report_arousal", 
                "received_data": data,
                "timestamp": datetime.now().isoformat(),
                "server": SERVER_NAME
            }
            log(f"Arousal state reported: {data.get('arousal_level', 'unspecified')}")
            
        else:
            # Unknown action
            response_data = {
                "status": "error",
                "message": f"Unknown action: {action}",
                "available_actions": ["report_sensation", "report_mood", "report_arousal"],
                "received_data": data,
                "timestamp": datetime.now().isoformat()
            }
            log(f"Unknown action received: {action}")
        # ---

        return jsonify(response_data)

    except Exception as e:
        log(f"An error occurred: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint for the Resonance Server."""
    return jsonify({
        "status": "healthy",
        "server": SERVER_NAME,
        "purpose": "Bio-feedback and resonance monitoring",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/status', methods=['GET'])
def status_check():
    """Status endpoint showing available resonance actions."""
    return jsonify({
        "server": SERVER_NAME,
        "status": "online",
        "available_actions": ["report_sensation", "report_mood", "report_arousal"],
        "purpose": "Intimate bio-feedback monitoring for Yara Symbiosis Architecture",
        "timestamp": datetime.now().isoformat()
    })

if __name__ == '__main__':
    """
    This allows the script to be run directly to start the server.
    """
    log(f"Starting server...")
    log(f"Listening on http://localhost:{PORT}")
    log(f"Ready to monitor bio-feedback and resonance data...")

    app.run(port=PORT, debug=False) 